import sys
import time
import json
import logging
import random
import re
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
import uvicorn
import undetected_chromedriver as uc
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="2.0")

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://445925a819f6.ngrok-free.app/",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data class for job information with proper typing"""
    job_id: Optional[str] = None
    title: Optional[str] = None
    company_name: Optional[str] = None
    location: Optional[str] = None
    experience: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    posted_date: Optional[str] = None
    total_views: Optional[str] = None
    job_description: Optional[str] = None
    company_description: Optional[str] = None
    skills: Optional[List[str]] = None
    industry: Optional[str] = None
    function: Optional[str] = None
    roles: Optional[str] = None
    job_url: Optional[str] = None
    scraped_at: Optional[str] = None

    def __post_init__(self):
        if self.scraped_at is None:
            self.scraped_at = datetime.now().isoformat()
        if self.skills is None:
            self.skills = []

class FounditScraper:
    """Foundit scraper with cookie handling and comprehensive job extraction"""
    
    def __init__(self, headless: bool = True, timeout: int = 60):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # Updated selectors for new foundit.in structure
        self.selectors = {
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',
            'job_search_input': '#Desktop-skillsAutoComplete--input',  # Updated ID
            'location_input': '#Desktop-locationAutoComplete--input',  # Updated ID
            'search_button': 'button[type="submit"], .search_submit_btn',
            'job_cards': 'div[data-index]',  # New structure uses data-index
            'job_card_container': '.flex.flex-col.gap-4.rounded-2xl',  # Individual job card
            'job_title': 'h3[title], h3.text-base.font-bold',
            'company_name': 'span p, .text-sm.font-normal p',
            'experience': 'label:contains("yrs"), svg + label',
            'location_card': 'svg[viewBox="0 0 24 24"] + label',
            'posted_date': 'label.text-xxs:contains("Posted"), .text-fontColor-content-tertiary',
            'apply_button': '#applyBtn, button:contains("Apply")',
            'save_button': 'button:contains("Save")',
            'company_logo': 'img[alt]',
            'next_page': '.pagination-next a, .next-page a, button:contains("Next")'
        }
    
    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with optimized options"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                options = uc.ChromeOptions()
                
                # Essential options for scraping
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--window-size=1920,1080')
                
                # Anti-detection options
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Performance options
                options.add_argument('--disable-images')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                options.add_argument('--disable-gpu')
                options.add_argument('--disable-web-security')
                options.add_argument('--disable-features=VizDisplayCompositor')
                options.add_argument('--disable-logging')
                options.add_argument('--disable-dev-tools')
                options.add_argument('--no-first-run')
                options.add_argument('--no-default-browser-check')
                options.add_argument('--disable-default-apps')
                options.add_argument('--disable-popup-blocking')
                options.add_argument('--ignore-certificate-errors')
                options.add_argument('--ignore-ssl-errors')
                options.add_argument('--ignore-certificate-errors-spki-list')
                
                # Additional options for remote access
                options.add_argument('--remote-debugging-port=9222')
                options.add_argument('--disable-background-timer-throttling')
                options.add_argument('--disable-backgrounding-occluded-windows')
                options.add_argument('--disable-renderer-backgrounding')
                options.add_argument('--disable-field-trial-config')
                options.add_argument('--disable-ipc-flooding-protection')
                
                # User agent rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
                options.add_argument(f'--user-agent={random.choice(user_agents)}')
                
                # Force headless mode for remote access
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--disable-gpu')
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                
                logger.info(f"Attempting to initialize Chrome driver (attempt {attempt + 1}/{max_retries})")
                
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, self.timeout)
                
                logger.info("Chrome driver initialized successfully")
                return self.driver
                
            except Exception as e:
                logger.error(f"Failed to setup driver (attempt {attempt + 1}/{max_retries}): {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                if attempt == max_retries - 1:
                    raise WebDriverException(f"Driver setup failed after {max_retries} attempts: {e}")
                
                # Wait before retry
                time.sleep(2)
        
        raise WebDriverException("Failed to initialize Chrome driver")
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def handle_cookies(self):
        """Handle cookie consent banner if present"""
        try:
            # Check if cookie banner exists
            cookie_banner = self.driver.find_element(By.CSS_SELECTOR, self.selectors['cookie_banner'])
            if cookie_banner.is_displayed():
                logger.info("Cookie banner found, accepting cookies...")
                accept_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
                accept_button.click()
                self.random_delay(1, 2)
                logger.info("Cookies accepted successfully")
        except NoSuchElementException:
            logger.info("No cookie banner found")
        except Exception as e:
            logger.warning(f"Error handling cookies: {e}")
    
    def safe_find_element(self, by: By, value: str, context=None) -> Optional[str]:
        """Safely find element and return text content"""
        try:
            element_context = context or self.driver
            element = element_context.find_element(by, value)
            return element.text.strip() if element.text else None
        except NoSuchElementException:
            return None
        except Exception as e:
            logger.warning(f"Error finding element {value}: {e}")
            return None
    
    def fill_search_form(self, job_title: str, location: str) -> bool:
        """Fill the search form on foundit.in homepage"""
        try:
            logger.info(f"Filling search form: {job_title} in {location}")

            # Navigate to homepage
            self.driver.get("https://www.foundit.in/")
            self.random_delay(2, 4)

            # Handle cookies first
            self.handle_cookies()

            # Find job title input using updated ID
            job_input = None
            job_input_ids = [
                'Desktop-skillsAutoComplete--input',  # Current ID
                'heroSectionDesktop-skillsAutoComplete--input'  # Fallback to old ID
            ]

            for input_id in job_input_ids:
                try:
                    job_input = self.driver.find_element(By.ID, input_id)
                    if job_input.is_displayed():
                        logger.info(f"Found job title input with ID: {input_id}")

                        # Clear field completely using multiple methods
                        job_input.clear()
                        self.random_delay(0.3, 0.5)

                        # Clear any existing value with Ctrl+A and Delete
                        job_input.send_keys(Keys.CONTROL + "a")
                        job_input.send_keys(Keys.DELETE)
                        self.random_delay(0.3, 0.5)

                        # Type the job title character by character to avoid autocomplete
                        for char in job_title:
                            job_input.send_keys(char)
                            time.sleep(0.05)  # Small delay between characters

                        self.random_delay(0.5, 1)

                        # Check if autocomplete dropdown appeared and dismiss it
                        try:
                            # Press Escape to close any autocomplete dropdown
                            job_input.send_keys(Keys.ESCAPE)
                            self.random_delay(0.3, 0.5)
                        except:
                            pass

                        # Verify the value was set correctly and remove any trailing commas
                        current_value = job_input.get_attribute('value')
                        logger.info(f"Job title field value after filling: '{current_value}'")

                        # If there's a trailing comma, remove it
                        if current_value.endswith(', ') or current_value.endswith(','):
                            logger.info("Removing trailing comma from job title field")
                            job_input.clear()
                            self.random_delay(0.2, 0.3)
                            clean_title = job_title.strip().rstrip(',').strip()
                            job_input.send_keys(clean_title)
                            self.random_delay(0.5, 1)
                            job_input.send_keys(Keys.ESCAPE)  # Dismiss autocomplete
                            current_value = job_input.get_attribute('value')
                            logger.info(f"Job title field value after cleaning: '{current_value}'")

                        logger.info(f"Successfully filled job title: {job_title}")
                        break

                except NoSuchElementException:
                    continue

            if not job_input:
                logger.warning("Could not find job title input field with any known ID")
                # Fallback to other selectors
                job_input_selectors = [
                    'input[placeholder*="Skills"]',
                    'input[placeholder*="Job Title"]',
                    'input[placeholder*="skill"]'
                ]

                for selector in job_input_selectors:
                    try:
                        job_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if job_input.is_displayed():
                            job_input.clear()
                            job_input.send_keys(Keys.CONTROL + "a")
                            job_input.send_keys(Keys.DELETE)
                            self.random_delay(0.3, 0.5)
                            job_input.send_keys(job_title)
                            logger.info(f"Filled job title: {job_title} using fallback selector: {selector}")
                            break
                    except NoSuchElementException:
                        continue

            # Find location input using updated ID
            location_input = None
            location_input_ids = [
                'Desktop-locationAutoComplete--input',  # Current ID
                'heroSectionDesktop-locationAutoComplete--input'  # Fallback to old ID
            ]

            for input_id in location_input_ids:
                try:
                    location_input = self.driver.find_element(By.ID, input_id)
                    if location_input.is_displayed():
                        logger.info(f"Found location input with ID: {input_id}")

                        # Clear field completely using multiple methods
                        location_input.clear()
                        self.random_delay(0.3, 0.5)

                        # Clear any existing value with Ctrl+A and Delete
                        location_input.send_keys(Keys.CONTROL + "a")
                        location_input.send_keys(Keys.DELETE)
                        self.random_delay(0.3, 0.5)

                        # Type the location character by character to avoid autocomplete
                        for char in location:
                            location_input.send_keys(char)
                            time.sleep(0.05)  # Small delay between characters

                        self.random_delay(0.5, 1)

                        # Check if autocomplete dropdown appeared and dismiss it
                        try:
                            # Press Escape to close any autocomplete dropdown
                            location_input.send_keys(Keys.ESCAPE)
                            self.random_delay(0.3, 0.5)
                        except:
                            pass

                        # Verify the value was set correctly and remove any trailing commas
                        current_value = location_input.get_attribute('value')
                        logger.info(f"Location field value after filling: '{current_value}'")

                        # If there's a trailing comma, remove it
                        if current_value.endswith(', ') or current_value.endswith(','):
                            logger.info("Removing trailing comma from location field")
                            location_input.clear()
                            self.random_delay(0.2, 0.3)
                            clean_location = location.strip().rstrip(',').strip()
                            location_input.send_keys(clean_location)
                            self.random_delay(0.5, 1)
                            location_input.send_keys(Keys.ESCAPE)  # Dismiss autocomplete
                            current_value = location_input.get_attribute('value')
                            logger.info(f"Location field value after cleaning: '{current_value}'")

                        logger.info(f"Successfully filled location: {location}")
                        break

                except NoSuchElementException:
                    continue

            if not location_input:
                logger.warning("Could not find location input field with any known ID")
                # Fallback to other selectors
                location_input_selectors = [
                    'input[placeholder="Location"]',
                    'input[placeholder*="location"]'
                ]

                for selector in location_input_selectors:
                    try:
                        location_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if location_input.is_displayed() and location_input != job_input:
                            location_input.clear()
                            location_input.send_keys(Keys.CONTROL + "a")
                            location_input.send_keys(Keys.DELETE)
                            self.random_delay(0.3, 0.5)
                            location_input.send_keys(location)
                            logger.info(f"Filled location: {location} using fallback selector: {selector}")
                            break
                    except NoSuchElementException:
                        continue

            # Wait a moment to ensure form fields are properly filled
            logger.info("Waiting before clicking search button to ensure form is ready...")
            self.random_delay(1, 2)

            # Find and click search button
            search_button = None
            search_button_selectors = [
                'button[type="submit"]',
                '.search_submit_btn',
                'button.search_submit_btn',
                'form button[type="submit"]'
            ]

            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if search_button.is_displayed() and search_button.is_enabled():
                        logger.info(f"Found search button with selector: {selector}")
                        break
                except NoSuchElementException:
                    continue

            if search_button:
                try:
                    # Scroll to button if needed
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", search_button)
                    self.random_delay(1, 2)

                    # Try clicking with JavaScript if regular click fails
                    try:
                        search_button.click()
                        logger.info("Search button clicked successfully")
                    except ElementClickInterceptedException:
                        logger.info("Regular click intercepted, trying JavaScript click")
                        self.driver.execute_script("arguments[0].click();", search_button)
                        logger.info("Search button clicked with JavaScript")

                except Exception as e:
                    logger.error(f"Error clicking search button: {e}")
                    # Fallback to Enter key
                    if job_input:
                        job_input.send_keys(Keys.RETURN)
                        logger.info("Used Enter key as fallback")
            else:
                logger.warning("Could not find search button, trying Enter key on job input")
                if job_input:
                    job_input.send_keys(Keys.RETURN)
                    logger.info("Pressed Enter key on job input field")

            # Wait for page navigation and search results to load
            logger.info("Waiting for page navigation and search results to load...")

            try:
                # Get initial URL
                original_url = self.driver.current_url
                logger.info(f"Original URL: {original_url}")

                # Wait a moment for navigation to start
                self.random_delay(1, 2)

                # Check current URL after navigation
                current_url = self.driver.current_url
                logger.info(f"Current URL after navigation: {current_url}")

                # Check if we're on a search results page (URL contains search parameters)
                if '/search/' in current_url and ('query=' in current_url or 'Data+Scientist' in current_url):
                    logger.info("Successfully navigated to search results page")
                elif current_url != original_url:
                    logger.info(f"URL changed to: {current_url}")
                else:
                    logger.warning("URL did not change, checking for search results on same page")

                # Wait for search results to appear
                logger.info("Waiting for search results to load...")
                self.random_delay(3, 5)

                # Try multiple selectors for search results with more specific targeting
                search_result_selectors = [
                    'div[data-index]',  # New job cards with data-index
                    '.flex.w-full.flex-col.gap-3',  # Job card wrapper
                    '.rounded-2xl.border-opacity-50.p-4',  # Individual job card
                    '.text-base.font-bold',  # Job title elements
                    'h3[title]',  # Job titles with title attribute
                    '.no-results',  # No results message
                    '[class*="job"]',  # Any element with "job" in class name
                    '.search-results'
                ]

                element_found = False
                found_selector = None
                found_count = 0

                for selector in search_result_selectors:
                    try:
                        elements = WebDriverWait(self.driver, 15).until(
                            EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                        )
                        if elements:
                            logger.info(f"Found {len(elements)} elements with selector: {selector}")
                            element_found = True
                            found_selector = selector
                            found_count = len(elements)
                            break
                    except TimeoutException:
                        logger.debug(f"Timeout waiting for selector: {selector}")
                        continue

                if element_found:
                    logger.info(f"Search results detected: {found_count} elements found with '{found_selector}'")
                else:
                    logger.warning("No search results found with any selector")

                    # Check page title for "0 jobs" or similar indicators
                    page_title = self.driver.title
                    logger.info(f"Page title: {page_title}")

                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.warning("Page title indicates no jobs found (0 results)")
                        return True  # This is a successful search with no results

                    # Check page source for no results indicators
                    try:
                        page_source = self.driver.page_source.lower()
                        no_results_indicators = [
                            '0 job vacancies',
                            'no jobs found',
                            'no results found',
                            'no matching jobs',
                            'sorry, no jobs found'
                        ]

                        for indicator in no_results_indicators:
                            if indicator in page_source:
                                logger.warning(f"Found 'no results' indicator in page source: '{indicator}'")
                                return True  # This is a successful search with no results

                        # Check for specific no results elements
                        no_results_selectors = [
                            '.no-results',
                            '[class*="no-result"]',
                            '.empty-state',
                            '[class*="empty"]'
                        ]

                        for no_result_selector in no_results_selectors:
                            try:
                                no_result_elem = self.driver.find_element(By.CSS_SELECTOR, no_result_selector)
                                if no_result_elem.is_displayed():
                                    logger.warning(f"Found 'no results' element with selector: {no_result_selector}")
                                    logger.warning(f"No results text: {no_result_elem.text}")
                                    return True  # This is a successful search with no results
                            except NoSuchElementException:
                                continue

                    except Exception as e:
                        logger.debug(f"Error checking for no results indicators: {e}")

                    # Log current page state for debugging
                    logger.info(f"Current URL after search: {self.driver.current_url}")
                    logger.info(f"Could not determine if this is a 'no results' page or a loading issue")

                self.random_delay(2, 3)
                logger.info("Search form processing completed")
                return True

            except Exception as e:
                logger.error(f"Error during search results loading: {e}")
                logger.info(f"Current URL: {self.driver.current_url}")
                logger.info(f"Page title: {self.driver.title}")
                return False

        except Exception as e:
            logger.error(f"Error filling search form: {e}")
            return False
    
    def extract_job_data_from_search_results(self) -> List[JobData]:
        """Extract job data directly from search results page (new approach)"""
        jobs = []
        try:
            # Wait for job cards to load with comprehensive selectors
            job_cards = []
            job_card_selectors = [
                'div[data-index]',  # Primary selector for new structure
                '.flex.w-full.flex-col.gap-3',  # Job card wrapper
                '.rounded-2xl.border-opacity-50.p-4',  # Individual job card container
                '.flex.flex-col.gap-4.rounded-2xl',  # Alternative job card
                self.selectors['job_cards'],  # Fallback to original selector
                '[class*="job-card"]',  # Any element with job-card in class
                '[class*="card"]'  # Any card element
            ]

            found_selector = None
            for selector in job_card_selectors:
                try:
                    potential_cards = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    if potential_cards:
                        job_cards = potential_cards
                        found_selector = selector
                        logger.info(f"Found {len(job_cards)} job cards with selector: {found_selector}")
                        break
                except TimeoutException:
                    logger.debug(f"No elements found with selector: {selector}")
                    continue

            if not job_cards:
                logger.warning("No job cards found with any selector")
                # Try to get page source snippet for debugging
                try:
                    page_source = self.driver.page_source
                    if 'no jobs found' in page_source.lower() or 'no results' in page_source.lower():
                        logger.warning("Page contains 'no jobs found' or 'no results' message")
                    else:
                        logger.info(f"Page source length: {len(page_source)} characters")
                        # Log first few div elements to understand structure
                        divs = self.driver.find_elements(By.TAG_NAME, 'div')[:10]
                        logger.info(f"Found {len(divs)} div elements on page")
                        for i, div in enumerate(divs[:5]):
                            classes = div.get_attribute('class') or 'no-class'
                            logger.debug(f"Div {i}: classes='{classes}'")
                except Exception as e:
                    logger.debug(f"Error analyzing page source: {e}")
                return jobs

            for index, card in enumerate(job_cards):
                try:
                    job = JobData()

                    # Extract job title
                    title_selectors = ['h3[title]', 'h3.text-base.font-bold', 'h3']
                    for title_sel in title_selectors:
                        try:
                            title_elem = card.find_element(By.CSS_SELECTOR, title_sel)
                            job.title = title_elem.get_attribute('title') or title_elem.text.strip()
                            if job.title:
                                break
                        except NoSuchElementException:
                            continue

                    # Extract company name
                    company_selectors = ['span p', '.text-sm.font-normal p', 'p']
                    for comp_sel in company_selectors:
                        try:
                            company_elem = card.find_element(By.CSS_SELECTOR, comp_sel)
                            job.company_name = company_elem.text.strip()
                            if job.company_name:
                                break
                        except NoSuchElementException:
                            continue

                    # Extract experience and location from labels
                    labels = card.find_elements(By.TAG_NAME, 'label')
                    for label in labels:
                        text = label.text.strip()
                        if 'yrs' in text.lower():
                            job.experience = text
                        elif any(city in text.lower() for city in ['bengaluru', 'bangalore', 'mumbai', 'delhi', 'chennai', 'hyderabad', 'pune']):
                            job.location = text
                        elif 'posted' in text.lower():
                            job.posted_date = text.replace('Posted', '').strip()

                    # Extract company logo
                    try:
                        logo_elem = card.find_element(By.CSS_SELECTOR, 'img[alt]')
                        job.company_description = f"Logo: {logo_elem.get_attribute('src')}"
                    except NoSuchElementException:
                        pass

                    # Set job ID as index for now (since new structure doesn't have explicit IDs)
                    job.job_id = str(index + 1)

                    # Set job URL to current search page URL
                    job.job_url = self.driver.current_url

                    if job.title and job.company_name:
                        jobs.append(job)
                        logger.debug(f"Extracted job: {job.title} at {job.company_name}")

                except Exception as e:
                    logger.warning(f"Error extracting data from job card {index}: {e}")
                    continue

            logger.info(f"Extracted {len(jobs)} jobs from search results")

        except TimeoutException:
            logger.error("Timeout waiting for job cards to load")
        except Exception as e:
            logger.error(f"Error extracting job data: {e}")

        return jobs
    

    
    def go_to_next_page(self) -> bool:
        """Navigate to next page of search results"""
        try:
            next_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['next_page'])
            if next_button.is_displayed() and next_button.is_enabled():
                next_button.click()
                self.random_delay(3, 5)
                
                # Wait for new page to load
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards'])))
                logger.info("Navigated to next page")
                return True
            else:
                logger.info("No next page button found or not clickable")
                return False
                
        except NoSuchElementException:
            logger.info("Next page button not found")
            return False
        except Exception as e:
            logger.warning(f"Error navigating to next page: {e}")
            return False
    
    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, List[Dict]]:
        """Main scraping function with improved error handling"""
        driver = None
        jobs = []
        scraped_count = 0
        page_num = 1
        
        try:
            logger.info(f"Starting scrape for '{job_title}' in '{location}' - Target: {num_jobs} jobs")
            
            # Setup driver with retry logic
            try:
                driver = self.setup_driver()
                self.driver = driver
            except Exception as e:
                logger.error(f"Failed to setup driver: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Driver setup failed: {str(e)}"}
            
            # Fill search form
            try:
                if not self.fill_search_form(job_title, location):
                    raise Exception("Failed to submit search form")
                logger.info("Search form submitted successfully, proceeding to extract jobs")
            except Exception as e:
                logger.error(f"Error during search form submission: {e}")
                logger.info(f"Current URL: {self.driver.current_url}")
                logger.info(f"Page title: {self.driver.title}")
                raise Exception(f"Failed to submit search form: {str(e)}")
            
            while scraped_count < num_jobs and page_num <= 10:  # Limit to 10 pages max
                logger.info(f"Scraping page {page_num}...")

                # Extract job data directly from current page (new approach)
                page_jobs = self.extract_job_data_from_search_results()

                if not page_jobs:
                    # Check if this is a "no results" page vs a loading issue
                    page_title = self.driver.title
                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.info("Search completed successfully - no jobs found for the given criteria")
                        break
                    else:
                        logger.warning("No jobs found on current page - might be a loading issue")
                        break

                logger.info(f"Found {len(page_jobs)} jobs on page {page_num}")

                # Add jobs to results
                for job_data in page_jobs:
                    if scraped_count >= num_jobs:
                        break

                    try:
                        if job_data.title:  # Only add if we got meaningful data
                            jobs.append(asdict(job_data))
                            scraped_count += 1
                            logger.info(f"Scraped job {scraped_count}/{num_jobs}: {job_data.title}")

                    except Exception as e:
                        logger.error(f"Error processing job: {e}")
                        continue

                # Try to navigate to next page
                if scraped_count < num_jobs:
                    if not self.go_to_next_page():
                        logger.info("No more pages available")
                        break
                    page_num += 1
                else:
                    break
            
            logger.info(f"Scraping completed. Total jobs scraped: {len(jobs)}")

            # Provide informative response based on results
            result = {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'requested': num_jobs}

            if len(jobs) == 0:
                # Check if this was a "no results" case
                page_title = self.driver.title if self.driver else ""
                if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                    result['message'] = f"No jobs found for '{job_title}' in '{location}' on Foundit. Try different search terms or location."
                    result['suggestion'] = "Try searching for 'Data Scientist' in 'Bangalore' or 'Mumbai' instead of 'India'"
                else:
                    result['message'] = "No jobs could be extracted. This might be due to page loading issues or changed website structure."

            return result
            
        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'error': str(e)}
        
        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("Driver closed")
                except Exception as e:
                    logger.warning(f"Error closing driver: {e}")

# API Endpoints
@app.get("/")
def root():
    return {
        "message": "Foundit Job Scraper API", 
        "version": "2.0",
        "status": "running",
        "timestamp": time.time(),
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

# Request model for POST endpoint
class FounditRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

@app.get("/scrape_foundit")
def scrape_foundit_api(
    job_title: str = Query(..., description="Job title to search for"),
    location: str = Query(..., description="Location to search in"),
    num_jobs: int = Query(5, ge=1, le=50, description="Number of jobs to scrape (1-50)")
):
    """
    Scrape job listings from Foundit (GET)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.post("/scrape_foundit")
def scrape_foundit_post_api(request: FounditRequest):
    """
    Scrape job listings from Foundit (POST)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(request.job_title, request.location, request.num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.get("/health")
def health_check():
    return {
        "status": "healthy", 
        "timestamp": time.time(),
        "service": "Foundit Scraper",
        "version": "2.0",
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

@app.options("/scrape_foundit")
async def options_scrape_foundit():
    return {"message": "OK"}

@app.options("/scrape_foundit")
async def options_scrape_foundit_post():
    return {"message": "OK"}

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run(app, host="0.0.0.0", port=8003, reload=True)
    else:
        # Command line usage
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Data Scientist"
        location = sys.argv[2] if len(sys.argv) > 2 else "India"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        
        scraper = FounditScraper(headless=False)  # Visible browser for debugging
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2)) 