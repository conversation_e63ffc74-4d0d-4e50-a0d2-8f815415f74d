import sys
import time
import json
import logging
import random
import re
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
import uvicorn
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
from urllib.parse import urljoin, quote_plus
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="2.0")

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://445925a819f6.ngrok-free.app/",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data class for job information with proper typing"""
    job_id: Optional[str] = None
    title: Optional[str] = None
    company_name: Optional[str] = None
    location: Optional[str] = None
    experience: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    posted_date: Optional[str] = None
    total_views: Optional[str] = None
    job_description: Optional[str] = None
    company_description: Optional[str] = None
    skills: Optional[List[str]] = None
    industry: Optional[str] = None
    function: Optional[str] = None
    roles: Optional[str] = None
    job_url: Optional[str] = None
    scraped_at: Optional[str] = None

    def __post_init__(self):
        if self.scraped_at is None:
            self.scraped_at = datetime.now().isoformat()
        if self.skills is None:
            self.skills = []

class FounditScraper:
    """Foundit scraper with cookie handling and comprehensive job extraction"""
    
    def __init__(self, headless: bool = True, timeout: int = 60):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # Selectors for foundit.in
        self.selectors = {
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',
            'job_search_input': '#heroSectionDesktop-skillsAutoComplete--input',
            'location_input': '#heroSectionDesktop-locationAutoComplete--input',
            'search_button': 'button[type="submit"]',
            'job_cards': '.srpResultCardContainer .cardContainer',
            'job_title': '.jobTitle',
            'company_name': '.companyName p',
            'experience': '.experienceSalary .bodyRow .details',
            'location_card': '.bodyRow .details.location',
            'job_id': '.cardContainer[id]',
            'job_description': '.job-description-content .jd-text',
            'company_description': '.card-panel p.color-grey-black.medium.text-justify.break-word',
            'skills': '.round-card a',
            'industry': '.job-detail-list .dt-content a',
            'function': '.job-detail-list .dt-content a',
            'roles': '.job-detail-list .dt-content a',
            'posted_date': '.posted.seprator',
            'total_views': '.posted.seprator',
            'job_id_detail': '.posted.pLR-10',
            'salary_detail': '.package span',
            'experience_detail': '.exp span',
            'location_detail': '.loc small a',
            'next_page': '.pagination-next a, .next-page a'
        }
    
    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with optimized options"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                options = uc.ChromeOptions()
                
                # Essential options for scraping
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--window-size=1920,1080')
                
                # Anti-detection options
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Performance options
                options.add_argument('--disable-images')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                options.add_argument('--disable-gpu')
                options.add_argument('--disable-web-security')
                options.add_argument('--disable-features=VizDisplayCompositor')
                options.add_argument('--disable-logging')
                options.add_argument('--disable-dev-tools')
                options.add_argument('--no-first-run')
                options.add_argument('--no-default-browser-check')
                options.add_argument('--disable-default-apps')
                options.add_argument('--disable-popup-blocking')
                options.add_argument('--ignore-certificate-errors')
                options.add_argument('--ignore-ssl-errors')
                options.add_argument('--ignore-certificate-errors-spki-list')
                
                # Additional options for remote access
                options.add_argument('--remote-debugging-port=9222')
                options.add_argument('--disable-background-timer-throttling')
                options.add_argument('--disable-backgrounding-occluded-windows')
                options.add_argument('--disable-renderer-backgrounding')
                options.add_argument('--disable-field-trial-config')
                options.add_argument('--disable-ipc-flooding-protection')
                
                # User agent rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
                options.add_argument(f'--user-agent={random.choice(user_agents)}')
                
                # Force headless mode for remote access
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--disable-gpu')
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                
                logger.info(f"Attempting to initialize Chrome driver (attempt {attempt + 1}/{max_retries})")
                
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, self.timeout)
                
                logger.info("Chrome driver initialized successfully")
                return self.driver
                
            except Exception as e:
                logger.error(f"Failed to setup driver (attempt {attempt + 1}/{max_retries}): {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                if attempt == max_retries - 1:
                    raise WebDriverException(f"Driver setup failed after {max_retries} attempts: {e}")
                
                # Wait before retry
                time.sleep(2)
        
        raise WebDriverException("Failed to initialize Chrome driver")
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def handle_cookies(self):
        """Handle cookie consent banner if present"""
        try:
            # Check if cookie banner exists
            cookie_banner = self.driver.find_element(By.CSS_SELECTOR, self.selectors['cookie_banner'])
            if cookie_banner.is_displayed():
                logger.info("Cookie banner found, accepting cookies...")
                accept_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
                accept_button.click()
                self.random_delay(1, 2)
                logger.info("Cookies accepted successfully")
        except NoSuchElementException:
            logger.info("No cookie banner found")
        except Exception as e:
            logger.warning(f"Error handling cookies: {e}")
    
    def safe_find_element(self, by: By, value: str, context=None) -> Optional[str]:
        """Safely find element and return text content"""
        try:
            element_context = context or self.driver
            element = element_context.find_element(by, value)
            return element.text.strip() if element.text else None
        except NoSuchElementException:
            return None
        except Exception as e:
            logger.warning(f"Error finding element {value}: {e}")
            return None
    
    def fill_search_form(self, job_title: str, location: str) -> bool:
        """Fill the search form on foundit.in homepage"""
        try:
            logger.info(f"Filling search form: {job_title} in {location}")

            # Navigate to homepage
            self.driver.get("https://www.foundit.in/")
            self.random_delay(2, 4)

            # Handle cookies first
            self.handle_cookies()

            # Find and fill job title input
            job_input = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_search_input'])
            job_input.clear()
            self.random_delay(0.5, 1)
            job_input.send_keys(job_title)
            logger.info(f"Filled job title: {job_title}")

            # Find and fill location input
            location_input = self.driver.find_element(By.CSS_SELECTOR, self.selectors['location_input'])
            location_input.clear()
            self.random_delay(0.5, 1)
            location_input.send_keys(location)
            logger.info(f"Filled location: {location}")

            # Click search button
            search_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['search_button'])
            search_button.click()
            logger.info("Search button clicked")

            # Wait for search results to load with multiple possible selectors
            logger.info("Waiting for search results to load...")
            try:
                # Try multiple selectors for search results
                search_result_selectors = [
                    self.selectors['job_cards'],
                    '.srpResultCardContainer',
                    '.cardContainer',
                    '.job-card',
                    '.search-results',
                    '.no-results'
                ]

                element_found = False
                for selector in search_result_selectors:
                    try:
                        WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        logger.info(f"Search results loaded with selector: {selector}")
                        element_found = True
                        break
                    except TimeoutException:
                        continue

                if not element_found:
                    logger.warning("No search results found with any selector, but continuing...")

            except Exception as e:
                logger.warning(f"Error waiting for search results: {e}, but continuing...")

            self.random_delay(3, 5)
            logger.info("Search form submitted successfully")
            return True

        except Exception as e:
            logger.error(f"Error filling search form: {e}")
            return False
    
    def extract_job_ids_from_search_results(self) -> List[str]:
        """Extract job IDs from search results page"""
        job_ids = []
        try:
            # Try multiple selectors for job cards
            job_card_selectors = [
                self.selectors['job_cards'],
                '.srpResultCardContainer .cardContainer',
                '.cardContainer',
                '.job-card',
                '[data-job-id]'
            ]

            job_cards = []
            for selector in job_card_selectors:
                try:
                    job_cards = self.wait.until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"Found job cards with selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not job_cards:
                logger.warning("No job cards found with any selector")
                return job_ids

            for card in job_cards:
                try:
                    # Try multiple methods to get job ID
                    job_id = None

                    # Method 1: Get from id attribute
                    job_id = card.get_attribute('id')
                    if job_id and job_id.isdigit():
                        job_ids.append(job_id)
                        logger.debug(f"Found job ID from id attribute: {job_id}")
                        continue

                    # Method 2: Get from data-job-id attribute
                    job_id = card.get_attribute('data-job-id')
                    if job_id and job_id.isdigit():
                        job_ids.append(job_id)
                        logger.debug(f"Found job ID from data-job-id: {job_id}")
                        continue

                    # Method 3: Extract from href links
                    links = card.find_elements(By.TAG_NAME, 'a')
                    for link in links:
                        href = link.get_attribute('href')
                        if href and 'job-details' in href and 'id=' in href:
                            import re
                            match = re.search(r'id=(\d+)', href)
                            if match:
                                job_id = match.group(1)
                                job_ids.append(job_id)
                                logger.debug(f"Found job ID from href: {job_id}")
                                break

                except Exception as e:
                    logger.warning(f"Error extracting job ID from card: {e}")
                    continue

            logger.info(f"Extracted {len(job_ids)} job IDs from search results")

        except TimeoutException:
            logger.error("Timeout waiting for job cards to load")
        except Exception as e:
            logger.error(f"Error extracting job IDs: {e}")

        return job_ids
    
    def scrape_job_details(self, job_id: str) -> JobData:
        """Scrape detailed job information from job page"""
        job = JobData(job_id=job_id)
        job_url = f"https://www.foundit.in/seeker/job-details?id={job_id}"
        job.job_url = job_url
        
        try:
            logger.info(f"Scraping job details from: {job_url}")
            self.driver.get(job_url)
            self.random_delay(2, 4)
            
            # Handle cookies on job page
            self.handle_cookies()
            
            # Wait for job content to load
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '.job-tittle-box')))
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Extract basic job information
            job.title = self.safe_find_element(By.CSS_SELECTOR, '.detail-job-tittle h1')
            job.company_name = self.safe_find_element(By.CSS_SELECTOR, '.detail-job-tittle span a')
            job.location = self.safe_find_element(By.CSS_SELECTOR, self.selectors['location_detail'])
            job.experience = self.safe_find_element(By.CSS_SELECTOR, self.selectors['experience_detail'])
            job.salary = self.safe_find_element(By.CSS_SELECTOR, self.selectors['salary_detail'])
            
            # Extract posted date and views
            posted_elements = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['posted_date'])
            for elem in posted_elements:
                text = elem.text.strip()
                if 'Posted On:' in text:
                    job.posted_date = text.replace('Posted On:', '').strip()
                elif 'Total Views:' in text:
                    job.total_views = text.replace('Total Views:', '').strip()
            
            # Extract job ID from detail page
            job_id_elem = self.safe_find_element(By.CSS_SELECTOR, self.selectors['job_id_detail'])
            if job_id_elem and 'Job Id:' in job_id_elem:
                job.job_id = job_id_elem.replace('Job Id:', '').strip()
            
            # Extract job description
            job_desc_elem = soup.select_one(self.selectors['job_description'])
            if job_desc_elem:
                job.job_description = job_desc_elem.get_text(strip=True)
            
            # Extract company description
            company_desc_elem = soup.select_one(self.selectors['company_description'])
            if company_desc_elem:
                job.company_description = company_desc_elem.get_text(strip=True)
            
            # Extract skills
            skills_elements = soup.select(self.selectors['skills'])
            job.skills = [elem.get_text(strip=True) for elem in skills_elements if elem.get_text(strip=True)]
            
            # Extract job details (industry, function, roles)
            detail_elements = soup.select(self.selectors['industry'])
            for elem in detail_elements:
                text = elem.get_text(strip=True)
                href = elem.get('href', '')
                
                if 'software' in href.lower():
                    job.industry = text
                elif 'it' in href.lower():
                    job.function = text
                elif 'business-analyst' in href.lower():
                    job.roles = text
            
            logger.info(f"Successfully scraped job: {job.title}")
            
        except TimeoutException:
            logger.error(f"Timeout while scraping job: {job_url}")
        except Exception as e:
            logger.error(f"Error scraping job details from {job_url}: {e}")
        
        return job
    
    def go_to_next_page(self) -> bool:
        """Navigate to next page of search results"""
        try:
            next_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['next_page'])
            if next_button.is_displayed() and next_button.is_enabled():
                next_button.click()
                self.random_delay(3, 5)
                
                # Wait for new page to load
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards'])))
                logger.info("Navigated to next page")
                return True
            else:
                logger.info("No next page button found or not clickable")
                return False
                
        except NoSuchElementException:
            logger.info("Next page button not found")
            return False
        except Exception as e:
            logger.warning(f"Error navigating to next page: {e}")
            return False
    
    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, List[Dict]]:
        """Main scraping function with improved error handling"""
        driver = None
        jobs = []
        scraped_count = 0
        page_num = 1
        
        try:
            logger.info(f"Starting scrape for '{job_title}' in '{location}' - Target: {num_jobs} jobs")
            
            # Setup driver with retry logic
            try:
                driver = self.setup_driver()
                self.driver = driver
            except Exception as e:
                logger.error(f"Failed to setup driver: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Driver setup failed: {str(e)}"}
            
            # Fill search form
            if not self.fill_search_form(job_title, location):
                raise Exception("Failed to submit search form")
            
            while scraped_count < num_jobs and page_num <= 10:  # Limit to 10 pages max
                logger.info(f"Scraping page {page_num}...")
                
                # Extract job IDs from current page
                job_ids = self.extract_job_ids_from_search_results()
                
                if not job_ids:
                    logger.warning("No job IDs found on current page")
                    break
                
                logger.info(f"Found {len(job_ids)} job IDs on page {page_num}")
                
                # Scrape individual jobs
                for job_id in job_ids:
                    if scraped_count >= num_jobs:
                        break
                    
                    try:
                        job_data = self.scrape_job_details(job_id)
                        if job_data.title:  # Only add if we got meaningful data
                            jobs.append(asdict(job_data))
                            scraped_count += 1
                            logger.info(f"Scraped job {scraped_count}/{num_jobs}: {job_data.title}")
                        
                        self.random_delay(1, 2)
                        
                    except Exception as e:
                        logger.error(f"Error processing job {job_id}: {e}")
                        continue
                
                # Try to navigate to next page
                if scraped_count < num_jobs:
                    if not self.go_to_next_page():
                        logger.info("No more pages available")
                        break
                    page_num += 1
                else:
                    break
            
            logger.info(f"Scraping completed. Total jobs scraped: {len(jobs)}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'requested': num_jobs}
            
        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'error': str(e)}
        
        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("Driver closed")
                except Exception as e:
                    logger.warning(f"Error closing driver: {e}")

# API Endpoints
@app.get("/")
def root():
    return {
        "message": "Foundit Job Scraper API", 
        "version": "2.0",
        "status": "running",
        "timestamp": time.time(),
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

# Request model for POST endpoint
class FounditRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

@app.get("/scrape_foundit")
def scrape_foundit_api(
    job_title: str = Query(..., description="Job title to search for"),
    location: str = Query(..., description="Location to search in"),
    num_jobs: int = Query(5, ge=1, le=50, description="Number of jobs to scrape (1-50)")
):
    """
    Scrape job listings from Foundit (GET)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.post("/scrape_foundit")
def scrape_foundit_post_api(request: FounditRequest):
    """
    Scrape job listings from Foundit (POST)
    """
    try:
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(request.job_title, request.location, request.num_jobs)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@app.get("/health")
def health_check():
    return {
        "status": "healthy", 
        "timestamp": time.time(),
        "service": "Foundit Scraper",
        "version": "2.0",
        "endpoints": {
            "GET /scrape_foundit": "Scrape jobs (GET)",
            "POST /scrape_foundit": "Scrape jobs (POST)",
            "GET /health": "Health check"
        }
    }

@app.options("/scrape_foundit")
async def options_scrape_foundit():
    return {"message": "OK"}

@app.options("/scrape_foundit")
async def options_scrape_foundit_post():
    return {"message": "OK"}

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run(app, host="0.0.0.0", port=8003, reload=True)
    else:
        # Command line usage
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Data Scientist"
        location = sys.argv[2] if len(sys.argv) > 2 else "India"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        
        scraper = FounditScraper(headless=False)  # Visible browser for debugging
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2)) 